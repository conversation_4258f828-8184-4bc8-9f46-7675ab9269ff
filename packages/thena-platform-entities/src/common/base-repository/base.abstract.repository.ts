import {
  DeepPartial,
  FindManyOptions,
  FindOneOptions,
  FindOptionsWhere,
  InsertResult,
  Repository,
  SelectQueryBuilder,
  UpdateResult,
} from "typeorm";
import type { QueryDeepPartialEntity } from "typeorm/query-builder/QueryPartialEntity.js";
import { UpsertOptions } from "typeorm/repository/UpsertOptions.js";
import { PagingQuery, PagingResult } from "../../common/cursor-paginate";
import {
  Paginate,
  PaginationOptionsInterface,
  PaginationResultsInterface,
} from "../../common/paginate";
import {
  BaseInterfaceRepository,
  Criteria,
  SoftDeleteOptions,
} from "./base.interface.repository";
import { TransactionContext } from "./transaction.interface";

/**
 * Interface for an entity that has an ID, note that at the time of creation
 * of an entity you might not have an ID, but once the entity is created it
 * must have an ID.
 * @template T The type of the entity
 */
interface HasId {
  id: string;
}

/**
 * @abstract
 * @class BaseAbstractRepository<T>
 * @implements {BaseInterfaceRepository<T>}
 * @template T
 * @description
 * An abstract base class for repositories, providing a foundation for entity-specific repositories.
 * This class implements common CRUD (Create, Read, Update, Delete) operations defined in the
 * BaseInterfaceRepository interface.
 *
 * @typeParam T - The entity type this repository manages. T must extend HasId, ensuring
 * that all entities have an id property.
 *
 * @remarks
 * This abstract class is designed to be extended by concrete repository classes for
 * specific entities. It provides a consistent structure and shared functionality
 * across different entity repositories.
 *
 * @example
 * ```ts
 * // Extending BaseAbstractRepository for a specific entity
 * class AccountRepository extends BaseAbstractRepository<Account>
 *   implements AccountRepositoryInterface {
 *   // Additional User-specific methods can be added here
 * }
 * ```
 *
 * @see {@link BaseInterfaceRepository} for the interface this class implements
 * @see {@link HasId} for the constraint on the generic type T
 */
export abstract class BaseAbstractRepository<T extends HasId>
  implements BaseInterfaceRepository<T>
{
  private entity: Repository<T>;

  /**
   * @constructor
   * @param entity The TypeORM repository for the entity
   * @protected
   */
  protected constructor(entity: Repository<T>) {
    this.entity = entity;
  }

  public createQueryBuilder(alias?: string): SelectQueryBuilder<T> {
    return this.entity.createQueryBuilder(alias);
  }

  public save(data: DeepPartial<T>): Promise<T> {
    return this.entity.save(data);
  }

  public saveWithTxn(
    transactionContext: TransactionContext,
    data: DeepPartial<T>,
  ): Promise<T> {
    console.log(
      `DEBUG: BaseRepository saveWithTxn - data:`,
      JSON.stringify(data, null, 2),
    );
    return transactionContext.manager.save(this.entity.target, data);
  }

  public saveMany(data: DeepPartial<T>[]): Promise<T[]> {
    return this.entity.save(data);
  }

  public saveManyWithTxn(
    transactionContext: TransactionContext,
    data: DeepPartial<T>[],
  ): Promise<T[]> {
    return transactionContext.manager.save(this.entity.target, data);
  }

  public insert(
    data: QueryDeepPartialEntity<T> | QueryDeepPartialEntity<T>[],
  ): Promise<InsertResult> {
    return this.entity.insert(data);
  }

  public insertWithTxn(
    transactionContext: TransactionContext,
    data: QueryDeepPartialEntity<T> | QueryDeepPartialEntity<T>[],
  ): Promise<InsertResult> {
    return transactionContext.manager.insert(this.entity.target, data);
  }

  public upsert(
    data: QueryDeepPartialEntity<T> | QueryDeepPartialEntity<T>[],
    options?: UpsertOptions<T>,
  ): Promise<InsertResult> {
    return this.entity.upsert(data, options);
  }

  public upsertWithTxn(
    transactionContext: TransactionContext,
    data: QueryDeepPartialEntity<T> | QueryDeepPartialEntity<T>[],
    options?: UpsertOptions<T>,
  ): Promise<InsertResult> {
    return transactionContext.manager.upsert(this.entity.target, data, options);
  }

  public update(
    criteria: Criteria<T>,
    partialEntity: QueryDeepPartialEntity<T>,
  ): Promise<UpdateResult> {
    return this.entity.update(criteria, partialEntity);
  }

  public updateWithTxn(
    transactionContext: TransactionContext,
    criteria: Criteria<T>,
    partialEntity: QueryDeepPartialEntity<T>,
  ): Promise<UpdateResult> {
    return transactionContext.manager.update(
      this.entity.target,
      criteria,
      partialEntity,
    );
  }

  public count(options?: FindManyOptions<T>): Promise<number> {
    return this.entity.count(options);
  }

  public create(data: DeepPartial<T>): T {
    return this.entity.create(data);
  }

  public createMany(data: DeepPartial<T>[]): T[] {
    return this.entity.create(data);
  }

  // eslint-disable-next-line max-len
  // @see https://stackoverflow.com/questions/71585167/typeorm-typescript-repository-findone-argument-of-type-is-not-assignable-to-pa
  public findOneById(id: any): Promise<T | null> {
    const options: FindOptionsWhere<T> = { id };
    return this.entity.findOneBy(options);
  }

  public findByCondition(
    filterCondition: FindOneOptions<T>,
  ): Promise<T | null> {
    return this.entity.findOne(filterCondition);
  }

  public findWithRelations(relations: FindManyOptions<T>): Promise<T[]> {
    return this.entity.find(relations);
  }

  public findAll(options?: FindManyOptions<T>): Promise<T[]> {
    return this.entity.find(options);
  }

  public restore(data: Criteria<T>): Promise<UpdateResult> {
    return this.entity.restore(data);
  }

  public restoreWithTxn(
    transactionContext: TransactionContext,
    data: Criteria<T>,
  ): Promise<UpdateResult> {
    return transactionContext.manager.restore(this.entity.target, data);
  }

  public remove(data: T): Promise<T> {
    return this.entity.remove(data);
  }

  public removeWithTxn(
    transactionContext: TransactionContext,
    data: T,
  ): Promise<T> {
    return transactionContext.manager.remove(this.entity.target, data);
  }

  public removeManyWithTxn(
    transactionContext: TransactionContext,
    data: T[],
  ): Promise<T[]> {
    return transactionContext.manager.remove(this.entity.target, data);
  }

  public softDelete(options: SoftDeleteOptions): Promise<UpdateResult> {
    return this.entity.softDelete(options as any);
  }

  public softDeleteWithTxn(
    transactionContext: TransactionContext,
    options: SoftDeleteOptions,
  ): Promise<UpdateResult> {
    return transactionContext.manager.softDelete(this.entity.target, options);
  }

  public exists(options: FindOneOptions<T>): Promise<boolean> {
    return this.entity.exists(options);
  }

  public softRemoveByConditionTxn(
    transactionContext: TransactionContext,
    filterCondition: FindManyOptions<T>,
  ) {
    return transactionContext.manager.softRemove(
      this.entity.target,
      filterCondition,
    );
  }

  public preload(entityLike: DeepPartial<T>): Promise<T | null> {
    return this.entity.preload(entityLike);
  }

  public queryRaw<TResult = any>(
    query: string,
    parameters?: any[],
  ): Promise<TResult> {
    return this.entity.query(query, parameters);
  }

  public async fetchPaginatedResults(
    paginationOptions: PaginationOptionsInterface,
    options?: FindManyOptions<T>,
  ): Promise<PaginationResultsInterface<T>> {
    const [results, total] = await this.entity.findAndCount({
      ...options,
      take: paginationOptions.limit,
      skip: paginationOptions.page * paginationOptions.limit,
    });

    return new Paginate<T>({ results, total });
  }

  public fetchWithCursor?(
    query: PagingQuery,
    where: FindOptionsWhere<T>,
    alias: string,
    relations?: Array<{ key: string; alias: string }>,
  ): Promise<PagingResult<T>>;
}
