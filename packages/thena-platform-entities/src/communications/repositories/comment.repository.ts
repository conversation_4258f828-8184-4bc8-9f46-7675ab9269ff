import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { TransactionContext } from "common/base-repository/transaction.interface";
import { DeepPartial, Repository } from "typeorm";
import { BaseAbstractRepository } from "../../common/base-repository/base.abstract.repository";
import { Comment } from "../entities/comments.entity";
import { CommentRepositoryInterface } from "./comment.repository.interface";

/**
 * @class CommentRepository
 * @extends BaseAbstractRepository<Comment>
 * @implements CommentRepositoryInterface
 * @description
 * Repository class for managing Comment entities in the database.
 * This class extends the BaseAbstractRepository to inherit common CRUD operations
 * and implements the CommentRepositoryInterface for type safety and consistency.
 *
 * @remarks
 * This repository is designed to work with NestJS and TypeORM. It uses the
 * `@Injectable()` decorator to allow dependency injection in NestJS modules.
 *
 * @example
 * ```ts
 * // Inject and use in a service
 * constructor(private commentRepository: CommentRepository) {}
 *
 * async findComment(id: number): Promise<Comment> {
 *   return this.commentRepository.findOneById(id);
 * }
 * ```
 *
 * @see {@link BaseAbstractRepository} for inherited methods
 * @see {@link CommentRepositoryInterface} for implemented methods
 * @see {@link Comment} for the entity this repository manages
 */
@Injectable()
export class CommentRepository
  extends BaseAbstractRepository<Comment>
  implements CommentRepositoryInterface
{
  constructor(
    @InjectRepository(Comment)
    private readonly commentRepository: Repository<Comment>,
  ) {
    super(commentRepository);
  }

  /**
   * Creates a new comment and commits it to the database
   * @param comment - The comment to create
   * @returns The created comment
   */
  async createComment(comment: DeepPartial<Comment>): Promise<Comment>;
  async createComment(
    comment: DeepPartial<Comment>,
    context: TransactionContext,
  ): Promise<Comment>;
  createComment(
    comment: DeepPartial<Comment>,
    context?: TransactionContext,
  ): Promise<Comment> {
    // Use a more visible error to ensure this shows up in logs
    throw new Error(
      `DEBUG: CommentRepository.createComment called with commentThreadName: "${comment.commentThreadName}"`,
    );

    const newComment = this.commentRepository.create(comment);

    if (context) {
      return this.saveWithTxn(context, newComment);
    }

    return this.save(newComment);
  }
}
