import Avatar from "@/components/common/Avatar";
import SlackColoredIcon from "@/components/icons/SlackColoredIcon";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { ThreadType } from "@/constants/thread";
import { useApiMutation } from "@/hooks/use-api-mutation";
import { cn } from "@/lib/utils";
import { POST_COMMENT_ON_TICKET } from "@/services/kanban";
import type { Editor } from "@tiptap/core";
import DOMPurify from "dompurify";
import {
  ChevronLeft,
  ChevronRight,
  Loader2,
  MessageCircle,
  MessageSquarePlus,
  Users,
} from "lucide-react";
import { useParams } from "next/navigation";
import { useEffect, useRef, useState } from "react";
import { toast } from "sonner";
import { useGlobalConfigPersistStore } from "../../../../../store/globalConfigPersistStore";
import Tiptap from "../Tiptap/Tiptap";

export interface SlackChannel {
  id: string;
  name: string;
  members?: number;
  teamId?: string;
  channelId?: string;
}
export interface PaginationMeta {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

const ThreadList = ({
  refetch,
  ticketId,
  internalThreads,
  activeThreadId,
  setActiveThreadId,
}) => {
  const params = useParams();
  const teamId = params.teamId as string;
  const currentUser = useGlobalConfigPersistStore((state) => state.currentUser);
  const [isCreationOpen, setIsCreationOpen] = useState(false);
  const [isCreationView, setIsCreationView] = useState(false);
  const [localFiles, setLocalFiles] = useState([]);
  const [content, setContent] = useState("");
  const [slackChannel, setSlackChannel] = useState("");
  const [initialMessage, setInitialMessage] = useState("");
  const [slackChannels, setSlackChannels] = useState<SlackChannel[]>([]);
  const [isLoadingChannels, setIsLoadingChannels] = useState(false);
  const [isLoadingMoreChannels, setIsLoadingMoreChannels] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [secretKey, setSecretKey] = useState<string | null>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const { mutate, isSuccess } = useApiMutation(POST_COMMENT_ON_TICKET);
  const [selectedChannel, setSelectedChannel] = useState<SlackChannel | null>(
    null,
  );

  // console.log("params", params);

  // Fetch Slack channels when the creation view is opened
  useEffect(() => {
    if (isCreationView) {
      // Reset pagination when opening the creation view
      setCurrentPage(1);
      setSlackChannels([]);
      fetchSlackAuth();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isCreationView]);

  // Initial auth fetch to get the secret key
  const fetchSlackAuth = async () => {
    setIsLoadingChannels(true);
    try {
      // Get sources to find the Slack source
      const sourcesResponse = await fetch("/api/workspace/sources");
      const { sources } = await sourcesResponse.json();

      // Find the first connected Slack source
      const slackSource = sources.find(
        (source) => source.isConnected && source.type === "slack",
      );

      if (!slackSource) {
        console.warn("No connected Slack source found");
        setSlackChannels([]);
        return;
      }

      // Get source details to get the secret key
      const sourceDetailsResponse = await fetch(
        `/api/workspace/sources/${slackSource.id}?botUserId=${slackSource.botUserId}&type=slack`,
      );
      const sourceDetails = await sourceDetailsResponse.json();

      // Extract the secret key
      const key = sourceDetails.key;

      if (!key) {
        console.warn("No secret key found for Slack integration");
        setSlackChannels([]);
        return;
      }

      // Store the secret key for pagination requests
      setSecretKey(key);

      // Now fetch the first page of channels
      await fetchSlackChannels(key, 1);
    } catch (error) {
      console.error("Error fetching Slack auth:", error);
      setSlackChannels([]);
      setIsLoadingChannels(false);
    }
  };

  const fetchSlackChannels = async (
    key: string,
    page: number,
    append = false,
  ) => {
    try {
      if (page > 1) {
        setIsLoadingMoreChannels(true);
      }

      // Directly fetch channels from the Slack API service using the secret key
      const channelsResponse = await fetch(
        `${process.env.NEXT_PUBLIC_THENA_SLACK_APP_URL || window.location.origin
        }/v1/slack/channel/${teamId}?page=${page}&limit=20`,
        {
          headers: {
            "x-auth-token": key,
          },
        },
      );

      if (!channelsResponse.ok) {
        throw new Error("Failed to fetch Slack channels");
      }

      const responseData = await channelsResponse.json();

      // Extract channels from the data property
      const channelsData = responseData.data || [];

      // Transform the channel data into our required format
      const newChannels = channelsData.map((channel) => ({
        id: channel.channelId || channel.id || channel.name,
        name: channel.name.startsWith("#") ? channel.name : `#${channel.name}`,
        members: channel.members || channel.memberCount,
        teamId: channel.teamId || channel.slackTeamId,
        channelId: channel.channelId,
      }));

      // Update pagination meta information
      const meta = responseData.meta as PaginationMeta;
      if (meta) {
        setCurrentPage(meta.page);
        setTotalPages(meta.totalPages || Math.ceil(meta.total / meta.limit));
      } else {
        console.warn("No pagination metadata received");
      }

      // Either replace or append the channels
      if (append) {
        setSlackChannels((prev) => [...prev, ...newChannels]);
      } else {
        setSlackChannels(newChannels);
      }
    } catch (error) {
      console.error("Error fetching Slack channels:", error);
      if (!append) {
        setSlackChannels([]);
      }
    } finally {
      setIsLoadingChannels(false);
      setIsLoadingMoreChannels(false);
    }
  };

  // Handle scrolling to load more channels
  const handleScroll = (e) => {
    const { scrollTop, scrollHeight, clientHeight } = e.target;
    // Check if we've scrolled to the bottom (with a smaller threshold)
    if (scrollHeight - scrollTop - clientHeight < 20) {
      console.log("Reached bottom, loading more channels...");
      loadMoreChannels();
    }
  };

  const loadMoreChannels = () => {
    // Check if we have more pages to load and we're not already loading
    if (currentPage < totalPages && !isLoadingMoreChannels && secretKey) {
      console.log(
        `Loading more channels: page ${currentPage + 1} of ${totalPages}`,
      );
      fetchSlackChannels(secretKey, currentPage + 1, true);
    }
  };

  const handleChannelSelect = (channelName: string) => {
    const channel = slackChannels.find((c) => c.name === channelName);
    setSlackChannel(channelName);
    setSelectedChannel(channel || null);
  };

  const createSlackThread = async (
    channelId: string,
    slackTeamId: string,
    messageText: string,
    platformCommentId: string,
  ) => {
    if (!secretKey) {
      console.error("No auth token available for Slack");
      return null;
    }

    // Get the current user persona
    const currentUserPersona = currentUser.allUserIds.find(
      (persona) => persona.uid === currentUser.uid,
    );

    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_THENA_SLACK_APP_URL}/v1/slack/channel/thread/${channelId}`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "x-auth-token": secretKey,
            "x-slack-id": slackTeamId,
          },
          body: JSON.stringify({
            platformTicketId: ticketId,
            message: messageText,
            platformCommentId: platformCommentId,
            commentAsEmail: currentUser.email,
            commentAsName: currentUserPersona.name,
            commentAsAvatar: currentUserPersona.avatarUrl,
          }),
        },
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error("Failed to create Slack thread:", errorData);
        return null;
      }

      const responseData = await response.json();
      console.log("Slack thread created successfully:", responseData);

      // Update the thread metadata with the Slack thread link
      if (responseData && responseData.threadTs && responseData.channelId) {
        // Construct the Slack thread URL
        const slackThreadLink = `https://${responseData.teamDomain
          }.slack.com/archives/${responseData.channelId
          }/p${responseData.threadTs.replace(".", "")}`;
        const channelName =
          responseData.channelName ||
          `#${responseData.channelName || "channel"}`;

        // Update the comment with the Slack thread link
        try {
          // Call the API to update the thread metadata
          await fetch(`/v1/comments/${platformCommentId}`, {
            method: "PATCH",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              metadata: {
                external_sinks: {
                  slack: {
                    slackThreadLink,
                    channelName,
                    threadTs: responseData.threadTs,
                    channelId: responseData.channelId,
                    teamId: responseData.teamId,
                    teamDomain: responseData.teamDomain,
                  },
                },
              },
            }),
          });
          console.log("Updated thread metadata with Slack link");
        } catch (error) {
          console.error("Failed to update thread with Slack link:", error);
        }
      }

      return responseData;
    } catch (error) {
      console.error("Error creating Slack thread:", error);
      return null;
    }
  };

  const addInternalThread = async (editor: Editor) => {
    if (!ticketId) {
      toast.error("Failed to send message");
      console.error(
        "Ticket ID is required",
        ticketId,
        new Date().toISOString(),
      );
      return;
    }
    if (editor.isEmpty) {
      toast.error("Thread cannot be empty");
      return null;
    }

    try {
      // For Slack threads, use the initial message as the content
      // For non-Slack threads, use the editor content
      const threadContent =
        slackChannel && initialMessage ? initialMessage : editor.getText();
      const threadContentHtml =
        slackChannel && initialMessage ? initialMessage : editor.getHTML();
      const threadContentJson =
        slackChannel && initialMessage ? initialMessage : editor.getJSON();

      // First create the internal thread to get its ID
      const createdThread = await mutate({
        content: threadContent,
        contentJson: JSON.stringify(threadContentJson),
        contentHtml: threadContentHtml,
        commentVisibility: "private",
        commentType: "comment",
        attachmentIds: localFiles,
        entityType: "ticket",
        entityId: ticketId,
        threadName: slackChannel
          ? `Slack: ${slackChannel}`
          : `Thread ${new Date().toISOString()}`,
      });

      // If Slack integration is enabled, create the Slack thread after we have the comment ID
      if (slackChannel && selectedChannel && initialMessage && createdThread) {
        // Check if the createdThread is an object and has a data property
        if (typeof createdThread !== "object" || !("data" in createdThread)) {
          console.error("No data returned from internal thread creation");
          toast.error("Failed to create internal thread");
          return;
        }

        // Check if the data property is an object and has an id property
        if (
          typeof createdThread.data !== "object" ||
          !("id" in createdThread.data) ||
          typeof createdThread.data.id !== "string"
        ) {
          console.error("No data returned from internal thread creation");
          toast.error("Failed to create internal thread");
          return;
        }

        // Extract the platform comment ID from the created thread
        const platformCommentId = createdThread.data.id;

        if (!platformCommentId) {
          console.error(
            "No platform comment ID available for Slack thread creation",
          );
          toast.error("Failed to create Slack thread: Missing comment ID");
          return;
        }

        try {
          const response = await createSlackThread(
            selectedChannel.channelId || "",
            selectedChannel.teamId || "",
            initialMessage,
            platformCommentId,
          );

          if (!response) {
            toast.error(
              "Created internal thread, but failed to create Slack thread.",
            );
          } else {
            console.log(
              "Successfully created both internal thread and Slack thread",
            );
          }
        } catch (error) {
          console.error("Error creating Slack thread:", error);
          toast.error(
            "Created internal thread, but failed to create Slack thread.",
          );
        }
      }

      // Reset the UI state
      setSlackChannel("");
      setInitialMessage("");
      setSelectedChannel(null);
      setIsCreationOpen(false);
      setIsCreationView(false);
    } catch (err) {
      console.error("Error creating internal thread:", err);
      toast.error("Failed to create internal thread");
    }
  };

  const handleBackToThreads = () => {
    setIsCreationView(false);
    setIsCreationOpen(false);
    setSlackChannel("");
    setInitialMessage("");
    setSelectedChannel(null);
  };

  useEffect(() => {
    if (isSuccess) {
      refetch();
      setContent(" ");
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isSuccess]);

  // Thread creation form view
  if (isCreationView) {
    return (
      <div className="space-y-6 p-4">
        <div>
          <Button
            variant="ghost"
            className="pl-0 flex items-center text-base"
            onClick={handleBackToThreads}
          >
            <ChevronLeft className="h-5 w-5 mr-1" />
            Back to threads
          </Button>
        </div>

        <div className="space-y-4" ref={contentRef}>
          <div className="space-y-2">
            <Label>
              Connect a Slack channel{" "}
              <span className="text-muted-foreground">(Optional)</span>
            </Label>
            <Select
              value={slackChannel}
              onValueChange={handleChannelSelect}
              disabled={isLoadingChannels && slackChannels.length === 0}
            >
              <SelectTrigger className="w-full rounded-md">
                <SelectValue
                  placeholder={
                    isLoadingChannels && slackChannels.length === 0
                      ? "Loading channels..."
                      : slackChannels.length === 0
                        ? "No Slack channels available"
                        : "Select a channel"
                  }
                />
              </SelectTrigger>
              <SelectContent
                className="max-h-[240px] overflow-auto"
                onScroll={handleScroll}
                position="popper"
                sideOffset={5}
              >
                {slackChannels.length === 0 && !isLoadingChannels ? (
                  <div className="py-2 px-2 text-sm text-muted-foreground">
                    No channels available. Connect a Slack workspace first.
                  </div>
                ) : (
                  <>
                    {slackChannels.map((channel) => (
                      <SelectItem key={channel.id} value={channel.name}>
                        {channel.name}{" "}
                        {channel.members ? `(${channel.members} members)` : ""}
                      </SelectItem>
                    ))}

                    {isLoadingMoreChannels && (
                      <div className="py-2 px-2 text-sm text-center flex items-center justify-center">
                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                        Loading more...
                      </div>
                    )}

                    {!isLoadingMoreChannels && currentPage < totalPages && (
                      <div
                        className="py-2 px-2 text-sm text-center text-blue-500 cursor-pointer hover:underline"
                        onClick={() => loadMoreChannels()}
                      >
                        Load more channels
                      </div>
                    )}
                  </>
                )}
              </SelectContent>
            </Select>
            {slackChannels.length === 0 && !isLoadingChannels && (
              <p className="text-xs text-muted-foreground mt-1">
                No Slack channels found. Please make sure your Slack workspace
                is connected.
              </p>
            )}
          </div>

          {slackChannel && (
            <div className="space-y-2">
              <Label>
                Initial message for the triage
                <span className="text-muted-foreground">
                  {" "}
                  (Required for Slack)
                </span>
              </Label>
              <Textarea
                placeholder="Enter the initial message to start the Slack triage thread..."
                value={initialMessage}
                onChange={(e) => setInitialMessage(e.target.value)}
                className="min-h-[100px] resize-none"
              />
              <p className="text-xs text-muted-foreground">
                This will be the first message in the Slack thread that others
                will respond to.
              </p>
            </div>
          )}

          <div className="pt-4">
            <Button
              className="w-full bg-primary text-primary-foreground h-10 rounded-md"
              onClick={() => {
                if (slackChannel && initialMessage.trim()) {
                  // For Slack threads, use a dummy editor to directly create the thread
                  const dummyEditor = {
                    isEmpty: false,
                    getHTML: () => initialMessage,
                  };
                  addInternalThread(dummyEditor as Editor);
                } else {
                  // For regular threads, just open the editor
                  setIsCreationOpen(true);
                }
              }}
              disabled={slackChannel !== "" && !initialMessage.trim()}
            >
              Start conversation
            </Button>
          </div>

          {isCreationOpen && !slackChannel && (
            <Tiptap
              content={content}
              setContent={setContent}
              localFiles={localFiles}
              setLocalFiles={setLocalFiles}
              onSend={(editor) => addInternalThread(editor)}
              className="flex-shrink-0 px-6 w-full"
              threadType={ThreadType.INTERNAL}
            />
          )}
        </div>
      </div>
    );
  }

  if (!internalThreads || internalThreads.length === 0) {
    return (
      <div className="w-full flex justify-center py-5 p-4">
        {isCreationOpen ? (
          <Tiptap
            content={content}
            setContent={setContent}
            localFiles={localFiles}
            setLocalFiles={setLocalFiles}
            onSend={(editor) => addInternalThread(editor)}
            className="flex-shrink-0 px-6 w-full"
            threadType={ThreadType.INTERNAL}
          />
        ) : (
          <div className="w-3/4 flex flex-col items-center gap-2">
            <MessageCircle size={24} />
            <div className="font-medium">No internal thread</div>
            <div className="text-center text-sm text-muted-foreground">
              Use internal thread to collaborate with your team members.
            </div>
            <Button
              variant="default"
              className="h-7 mt-4"
              // onClick={handleCreateThread}
              onClick={() => setActiveThreadId("new")}
            >
              <span className="mr-2">
                <MessageSquarePlus size={16} />
              </span>{" "}
              New thread
            </Button>
          </div>
        )}
      </div>
    );
  }

  const sortedThreads = internalThreads.sort((a, b) => {
    const dateA = new Date(a.createdAt);
    const dateB = new Date(b.createdAt);
    return dateB.getTime() - dateA.getTime();
  });

  return (
    <div className="space-y-4 h-full flex flex-col">
      <div className="flex items-center justify-center pt-4 pb-0 flex-shrink-0">
        <Button
          onClick={() => setActiveThreadId("new")}
          variant="outline"
          className="rounded-sm h-8 px-3 border-dashed border-color-border thread-hover w-full"
        >
          <span className="mr-2">
            <MessageSquarePlus size={16} />
          </span>{" "}
          New thread
        </Button>
      </div>

      {isCreationOpen && (
        <Tiptap
          content={content}
          setContent={setContent}
          localFiles={localFiles}
          setLocalFiles={setLocalFiles}
          onSend={(editor) => addInternalThread(editor)}
          className="flex-shrink-0 px-6 w-full"
          threadType={ThreadType.INTERNAL}
        />
      )}

      <div className="space-y-3 flex-grow pr-1">
        {sortedThreads.map((thread, id: number) => {
          console.log("thread obj", thread)
          const authorName = thread.author || "Unknown";
          const authorAvatar = thread.authorAvatarUrl || "";

          // Get unique participants (up to 3 for display)
          const hasReplies = thread.replies && thread.replies.length > 0;
          const participants = hasReplies
            ? [
              { name: authorName, avatar: authorAvatar },
              ...thread.replies.map((reply) => ({
                name: reply.author || "",
                avatar: reply.authorAvatarUrl || "",
              })),
            ]
            : [{ name: authorName, avatar: authorAvatar }];

          // Get unique participants by name
          const uniqueParticipants = participants.filter(
            (participant, index, self) =>
              index === self.findIndex((p) => p.name === participant.name),
          );

          // Total number of unique participants
          const totalParticipants = uniqueParticipants.length;

          // Display up to 3 participants
          const displayParticipants = uniqueParticipants.slice(0, 3);

          return (
            <Card
              key={id}
              className={cn(
                "px-4 py-3 cursor-pointer transition-colors rounded-sm thread-card",
                activeThreadId === thread.id && "border-primary",
              )}
              onClick={() => setActiveThreadId(thread.id)}
            >
              <div className="flex items-center gap-2">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <span className="font-medium text-sm truncate flex items-center gap-1.5">
                      {thread.threadName?.startsWith("Slack:") ||
                        thread.metadata?.external_sinks?.slack
                          ?.slackThreadLink ? (
                        <SlackColoredIcon size={14} />
                      ) : (
                        <Users size={14} className="text-primary" />
                      )}
                      {thread.threadName?.startsWith("Slack:")
                        ? thread.threadName.replace("Slack:", "").trim()
                        : thread.metadata?.external_sinks?.slack
                          ?.slackThreadLink
                          ? thread.content?.includes("#")
                            ? `#${thread.content.split("#")[1].split(" ")[0]}`
                            : "Slack thread"
                          : thread.threadName || "Internal thread"}
                    </span>
                    <ChevronRight className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                  </div>
                  <div className="flex items-center gap-2 mt-1">
                    <p className="text-muted-foreground text-xs truncate flex-1">
                      {DOMPurify.sanitize(thread.contentHtml).replace(
                        /<[^>]*>/g,
                        " ",
                      )}
                    </p>
                    <div className="flex items-center -space-x-2 flex-shrink-0">
                      {displayParticipants.map((participant, index) => (
                        <Avatar
                          key={index}
                          src={participant.avatar}
                          fallbackText={participant.name}
                          imgClassnames="h-5 w-5 border-2 border-background"
                        />
                      ))}
                      {totalParticipants > 3 && (
                        <div className="h-5 w-5 rounded-full bg-muted text-xs flex items-center justify-center border-2 border-background text-muted-foreground">
                          +{totalParticipants - 3}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          );
        })}
      </div>
    </div>
  );
};

export default ThreadList;
