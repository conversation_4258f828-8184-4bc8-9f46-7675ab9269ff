import Avatar from "@/components/common/Avatar";
import SlackColoredIcon from "@/components/icons/SlackColoredIcon";
import { Button } from "@/components/ui/button";
import { useApi } from "@/hooks/use-api";
import { useApiMutation } from "@/hooks/use-api-mutation";
import {
  ADD_REACTION,
  DELETE_COMMENT_BY_ID,
  GET_TICKET_COMMENTS_REPLY,
  POST_COMMENT_ON_TICKET,
  UPDATE_COMMENT_BY_ID,
} from "@/services/kanban";
import { useTicketDrawerStore } from "@/store/ticketDrawerStore";
import type { Comment } from "@/types/kanban";
import { getTimeAgo, highlightMentions } from "@/utils/kanban";
import { useInfiniteQuery } from "@tanstack/react-query";
import type { Editor } from "@tiptap/core";
import { format } from "date-fns";
import DOMPurify from "dompurify";
import {
  ChevronRight,
  ExternalLink,
  Loader2,
  Search,
  Users,
} from "lucide-react";
import { useParams } from "next/navigation";
import { Fragment, useEffect, useRef, useState } from "react";
import { toast } from "sonner";
import styled from "styled-components";
import { ThreadType } from "../../../../../constants/thread";
import { useEmojiPicker } from "../../../../../hooks/use-emoji-picker";
import { useGlobalConfigPersistStore } from "../../../../../store/globalConfigPersistStore";
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import TooltipWrapper from "../../../../tooltip-wrapper";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../../../ui/select";
import AttachmentPreview from "../AttachmentPreview";
import EmojiPicker from "../Conversations/EmojiPicker";
import Reactions from "../Conversations/Reactions";
import Tiptap from "../Tiptap/Tiptap";
import MessageActions from "./MessageActions";
import type { PaginationMeta, SlackChannel } from "./ThreadList";

const ConversationWrapper = styled.div`
  /* Specific styling for links in Lumen theme */
  a {
    color: var(--url-color);
  }

  ol,
  ul {
    margin-left: 15px;
  }

  word-break: break-word;
  overflow-wrap: anywhere;

  p:empty {
    padding-top: 16px;
  }

  blockquote {
    color: var(--color-text);
    border-left: 4px solid var(--slack-blockquote-left-border);
    padding-left: 12px;
    margin: 8px 0;
    background-color: var(--slack-code-block-bg);
    border-radius: 4px;
  }

  blockquote p {
    color: var(--color-text);
    padding: 8px 0;
  }

  h2 {
    font-size: 1.5rem; /* 24px */
    line-height: 2rem; /* 32px */
    font-weight: 500;
  }

  h3 {
    font-size: 1.25rem; /* 20px */
    line-height: 1.75rem; /* 28px */
    font-weight: 500;
  }

  p {
    font-size: 14px; /* 14px */
    line-height: 1.5rem; /* 24px */
  }

  p.is-empty::before {
    color: var(--color-text-muted);
    content: attr(data-placeholder);
    float: left;
    height: 0;
    pointer-events: none;
  }

  code {
    font-size: 0.875rem; /* 14px */
    font-family: monospace;
  }

  ul,
  ol {
    padding: 0 1rem;
    margin: 0rem 1rem 0rem 0.4rem;

    li p {
      margin-top: 0.25em;
      margin-bottom: 0.25em;
    }
  }

  hr[contenteditable="false"] {
    margin-bottom: 0;
    border-top: 1px solid var(--color-border);
  }

  .mention {
    color: var(--mention-color);
    background-color: var(--mention-bg);
    padding: var(--mention-padding);
    border-radius: var(--mention-border-radius);
    font-weight: var(--mention-font-weight);
  }
`;

interface SlackChannelResponse {
  channels: SlackChannel[];
  meta: PaginationMeta;
  nextPage?: number;
}

const useSlackChannels = (teamId: string, secretKey: string) => {
  return useInfiniteQuery<SlackChannelResponse, Error>({
    queryKey: ["slack-channels", teamId],
    queryFn: async ({ pageParam = 1 }) => {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_THENA_SLACK_APP_URL || window.location.origin
        }/v1/slack/channel/${teamId}?page=${pageParam}&limit=1000`,
        {
          headers: {
            "x-auth-token": secretKey,
          },
        },
      );

      if (!response.ok) {
        throw new Error("Failed to fetch Slack channels");
      }

      const responseData = await response.json();
      const channelsData = responseData.data || [];

      return {
        channels: channelsData.map((channel) => ({
          id: channel.channelId || channel.id || channel.name,
          name: channel.name.startsWith("#")
            ? channel.name
            : `#${channel.name}`,
          members: channel.members || channel.memberCount,
          teamId: channel.teamId || channel.slackTeamId,
          channelId: channel.channelId,
        })),
        meta: responseData.meta,
        nextPage:
          responseData.meta?.page < responseData.meta?.totalPages
            ? responseData.meta?.page + 1
            : undefined,
      };
    },
    enabled: !!secretKey && !!teamId,
    staleTime: Number.POSITIVE_INFINITY, // Consider data stale never
    getNextPageParam: (lastPage) => lastPage.nextPage,
    initialPageParam: 1,
    refetchOnWindowFocus: false,
  });
};

const ActiveThread = ({
  internalThreads,
  activeThreadId,
  setActiveThreadId,
  ticketId,
  internalThreadRefetch,
}) => {
  const [content, setContent] = useState("");
  const [localFiles, setLocalFiles] = useState([]);
  const [editMessageId, setEditMessageId] = useState(null);
  const currentUserId = useGlobalConfigPersistStore(
    (state) => state.currentUser.uid,
  );

  const [editOpen, setEditOpen] = useState({
    id: null,
    state: false,
  });

  const [emojiPickerOpenId, setEmojiPickerOpenId] = useState(null);

  // Create a separate emoji picker state
  const [emojiMessage, setEmojiMessage] = useState<string | null>(null);

  // This will be used when an emoji is selected
  const { mutate: addEmoji } = useApiMutation(
    emojiMessage ? ADD_REACTION(emojiMessage) : "",
    {},
    "POST",
  );

  const [slackChannel, setSlackChannel] = useState("");
  const [selectedChannel, setSelectedChannel] = useState<SlackChannel | null>(
    null,
  );
  const [secretKey, setSecretKey] = useState<string | null>(null);
  const currentUser = useGlobalConfigPersistStore((state) => state.currentUser);

  const scrollRef = useRef<HTMLDivElement>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const contentRef = useRef<HTMLDivElement>(null);
  const params = useParams();
  const teamId = params.teamId as string;

  const [editLocalFiles, setEditLocalFiles] = useState([]);
  const [deleteMessageId, setDeleteMessageId] = useState(null);
  const thread: Comment = (internalThreads || []).find(
    (t: Comment) => t.id === activeThreadId,
  );
  const forceUpdateReply = useTicketDrawerStore(
    (state) => state.forceUpdateReply,
  );

  const { mutate: postComment, isSuccess } = useApiMutation(
    POST_COMMENT_ON_TICKET,
  );
  const data = useEmojiPicker();

  const { data: replyData, refetch } = useApi<Comment[]>(
    GET_TICKET_COMMENTS_REPLY(thread?.id || activeThreadId),
    {},
    { enabled: true, isNextApi: false },
    forceUpdateReply,
  );

  const { mutate: deleteMessage } = useApiMutation(
    DELETE_COMMENT_BY_ID(deleteMessageId),
    {},
    "DELETE",
  );

  const { mutate: updateMessage } = useApiMutation(
    UPDATE_COMMENT_BY_ID(editMessageId),
    {},
    "PATCH",
  );

  // Create a ref to store the current state values
  const stateRef = useRef({
    slackChannel,
    selectedChannel,
    activeThreadId,
    secretKey,
  });

  // Update the ref whenever these values change
  useEffect(() => {
    stateRef.current = {
      slackChannel,
      selectedChannel,
      activeThreadId,
      secretKey,
    };
  }, [slackChannel, selectedChannel, activeThreadId, secretKey]);

  useEffect(() => {
    if (activeThreadId === "new") {
      // Reset pagination when opening the creation view
      fetchSlackAuth();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeThreadId]);

  const fetchSlackAuth = async () => {
    try {
      // Get sources to find the Slack source
      const sourcesResponse = await fetch("/api/workspace/sources");
      const { sources } = await sourcesResponse.json();

      // Find the first connected Slack source
      const slackSource = sources.find(
        (source) => source.isConnected && source.type === "slack",
      );

      if (!slackSource) {
        console.warn("No connected Slack source found");
        setSlackChannels([]);
        return;
      }

      // Get source details to get the secret key
      const sourceDetailsResponse = await fetch(
        `/api/workspace/sources/${slackSource.id}?botUserId=${slackSource.botUserId}&type=slack`,
      );
      const sourceDetails = await sourceDetailsResponse.json();

      // Extract the secret key
      const key = sourceDetails.key;

      if (!key) {
        console.warn("No secret key found for Slack integration");
        setSlackChannels([]);
        return;
      }

      // Store the secret key for pagination requests
      setSecretKey(key);
    } catch (error) {
      console.error("Error fetching Slack auth:", error);
      setSlackChannels([]);
    }
  };

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    if (isSuccess) {
      refetch();
      setContent(" ");
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isSuccess]);

  const { mutate } = useApiMutation<{ data: Comment }>(POST_COMMENT_ON_TICKET);

  // if (!thread) return null;

  const createSlackThread = async (
    channelId: string,
    slackTeamId: string,
    messageText: string,
    platformCommentId: string,
  ) => {
    if (!channelId || !slackTeamId || !messageText) {
      console.error(
        "Missing required parameters for Slack thread creation",
        channelId,
        slackTeamId,
        messageText,
      );
      return null;
    }

    if (!stateRef.current?.secretKey) {
      console.error("No auth token available for Slack");
      return null;
    }

    // Get the current user persona
    const currentUserPersona = currentUser.allUserIds.find(
      (persona) => persona.uid === currentUser.uid,
    );

    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_THENA_SLACK_APP_URL}/v1/slack/channel/thread/${channelId}`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "x-auth-token": stateRef.current?.secretKey,
            "x-slack-id": slackTeamId,
          },
          body: JSON.stringify({
            platformTicketId: ticketId,
            message: messageText,
            platformCommentId: platformCommentId,
            commentAsEmail: currentUser.email,
            commentAsName: currentUserPersona.name,
            commentAsAvatar: currentUserPersona.avatarUrl,
          }),
        },
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error("Failed to create Slack thread:", errorData);
        return null;
      }

      const responseData = await response.json();
      console.log("Slack thread created successfully:", responseData);
      return responseData;
    } catch (error) {
      console.error("Error creating Slack thread:", error);
      return null;
    }
  };

  const addSlackThread = async (editor: Editor) => {
    if (!ticketId) {
      toast.error("Failed to send message");
      console.error(
        "Ticket ID is required",
        ticketId,
        new Date().toISOString(),
      );
      return;
    }
    // Use the current values from the ref instead of directly from state
    const currentState = stateRef.current;

    const threadContent = editor.getText();
    const threadContentHtml = editor.getHTML();
    const threadContentJson = editor.getJSON();

    const createdThread = await mutate({
      content: threadContent,
      contentJson: JSON.stringify(threadContentJson),
      contentHtml: threadContentHtml,
      commentVisibility: "private",
      commentType: "comment",
      attachmentIds: localFiles?.map((file) => file.id),
      entityType: "ticket",
      entityId: ticketId,
      threadName: currentState.selectedChannel
        ? currentState.slackChannel && currentState.slackChannel.trim()
          ? `Slack: ${currentState.slackChannel.trim()}`
          : `Thread ${new Date().toISOString()}`
        : null,
    });

    // Process JIRA links if the thread was created successfully
    if (createdThread?.data && ticketId) {
      import("@/utils/jira-utils")
        .then(({ processJiraLinks }) => {
          processJiraLinks(threadContentHtml, ticketId);
        })
        .catch((error) => {
          console.error("Error importing JIRA utils:", error);
        });
    }

    internalThreadRefetch();
    setActiveThreadId(createdThread.data.id);
    setLocalFiles([]);

    editor.commands.clearContent();

    if (
      currentState.slackChannel &&
      currentState.slackChannel.trim() &&
      currentState.selectedChannel &&
      createdThread
    ) {
      // Check if the createdThread is an object and has a data property
      if (typeof createdThread !== "object" || !("data" in createdThread)) {
        console.error("No data returned from internal thread creation");
        toast.error("Failed to create internal thread");
        return;
      }

      // Check if the data property is an object and has an id property
      if (
        typeof createdThread.data !== "object" ||
        !("id" in createdThread.data) ||
        typeof createdThread.data.id !== "string"
      ) {
        console.error("No data returned from internal thread creation");
        toast.error("Failed to create internal thread");
        return;
      }

      // Extract the platform comment ID from the created thread
      const platformCommentId = createdThread.data.id;

      if (!platformCommentId) {
        console.error(
          "No platform comment ID available for Slack thread creation",
        );
        toast.error("Failed to create Slack thread: Missing comment ID");
        return;
      }

      try {
        const response = await createSlackThread(
          currentState.selectedChannel.channelId || "",
          currentState.selectedChannel.teamId || "",
          threadContent,
          platformCommentId,
        );

        if (!response) {
          toast.error(
            "Created internal thread, but failed to create Slack thread.",
          );
        } else {
          console.log(
            "Successfully created both internal thread and Slack thread",
          );
        }
      } catch (error) {
        console.error("Error creating Slack thread:", error);
        toast.error(
          "Created internal thread, but failed to create Slack thread.",
        );
      }
    }
  };

  const addReplyToComment = async (editor: Editor) => {
    if (!ticketId) {
      toast.error("Failed to send message");
      console.error(
        "Ticket ID is required",
        ticketId,
        new Date().toISOString(),
      );
      return;
    }

    if (editor.isEmpty && localFiles.length === 0) {
      toast.error("Reply cannot be empty");
      return null;
    }

    // Use the current values from the ref instead of directly from state
    const currentState = stateRef.current;

    if (currentState.activeThreadId === "new") {
      return addSlackThread(editor);
    }
    try {
      const contentHtml = editor.getHTML();
      const response = await postComment({
        content: editor.getText(),
        contentJson: JSON.stringify(editor.getJSON()),
        contentHtml: contentHtml,
        commentVisibility: "private",
        commentType: "comment",
        parentCommentId: thread.id,
        attachmentIds: localFiles?.map((file) => file.id),
        entityType: "ticket",
        entityId: ticketId,
      });

      // Process JIRA links if the reply was posted successfully
      if (response && ticketId) {
        import("@/utils/jira-utils")
          .then(({ processJiraLinks }) => {
            processJiraLinks(contentHtml, ticketId);
          })
          .catch((error) => {
            console.error("Error importing JIRA utils:", error);
          });
      }

      editor.commands.clearContent();
      setLocalFiles([]);
    } catch (err) {
      console.error("Error posting reply to comment:", err);
    }
  };

  const handleDeleteMessage = async (messageId) => {
    try {
      setDeleteMessageId(messageId);
      await deleteMessage();
      refetch();
      toast.success("Message deleted successfully");
      setDeleteMessageId(null);
    } catch (err) {
      console.error("Error deleting message:", err);
      toast.error("Failed to delete message");
      setDeleteMessageId(null);
    }
  };

  const handleEditMessage = (message) => {
    setEditMessageId(message.id);
    setEditLocalFiles(
      message.attachments?.map((item) => ({
        url: item.url,
        type: item.contentType,
      })) || [],
    );
  };

  const saveEditedMessage = async (editor, messageId) => {
    if (editor.isEmpty && editLocalFiles.length === 0) {
      toast.error("Message cannot be empty");
      return;
    }

    try {
      setEditMessageId(messageId);

      // Get the content directly from the editor
      const contentText = editor.getText();
      const contentHtml = editor.getHTML();
      const contentJson = editor.getJSON();


      // We need to send the HTML content directly without processing
      await updateMessage({
        content: contentText,
        contentHtml: contentHtml,
        contentJson: JSON.stringify(contentJson),
        // Don't rely on preserveMentions as it might not be correctly implemented in the backend
      });

      refetch();
      setEditOpen({ id: null, state: false });
    } catch (err) {
      console.error("Error updating message:", err);
      toast.error("Failed to update message");
    } finally {
      setEditMessageId(null);
    }
  };

  const handleChannelSelect = (channelName: string) => {
    const channel = slackChannels.find((c) => c.name === channelName);
    setSlackChannel(channelName);
    setSelectedChannel(channel || null);
  };

  const handleScroll = (e) => {
    const { scrollTop, scrollHeight, clientHeight } = e.target;
    // Check if we've scrolled to the bottom (with a smaller threshold)
    if (scrollHeight - scrollTop - clientHeight < 1000) {
      loadMoreChannels();
    }
  };

  // Replace the fetchSlackChannels function with this hook
  const {
    data: slackChannelsData,
    isPending,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = useSlackChannels(teamId, secretKey);

  // Update the slackChannels state to use the data from React Query
  const [slackChannels, setSlackChannels] = useState<SlackChannel[]>([]);

  useEffect(() => {
    if (slackChannelsData?.pages) {
      const allChannels = slackChannelsData.pages.flatMap(
        (page) => page.channels,
      );
      setSlackChannels(allChannels);
    }
  }, [slackChannelsData]);

  // Update the loadMoreChannels function
  const loadMoreChannels = () => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  };

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const userName = thread?.impersonatedUserName ?? thread?.author;
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const avatarUrl = thread?.impersonatedUserAvatar ?? thread?.authorAvatarUrl;
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const isAllowedToEdit = thread?.authorId === currentUserId;

  useEffect(() => {
    if (replyData?.length > 0) {
      scrollRef.current?.scrollIntoView({ behavior: "smooth" });
    }
  }, [replyData?.length]);


  return (
    <div className="h-full flex flex-col overflow-hidden">
      <div className="border-b px-4 py-3 flex-shrink-0 bg-background">
        <div className="flex items-center justify-between">
          {activeThreadId === "new" ? (
            <div className="flex-1"></div>
          ) : (
            thread && (
              <h3 className="font-medium flex items-center gap-1.5">
                {thread.threadName?.startsWith("Slack:") ||
                  thread.metadata?.external_sinks?.slack?.slackThreadLink ? (
                  <SlackColoredIcon size={16} />
                ) : (
                  <Users size={16} className="text-primary" />
                )}
                {(() => {
                  let channelName = "";
                  if (thread.threadName?.startsWith("Slack:")) {
                    channelName = thread.threadName
                      .replace("Slack:", "")
                      .trim() || "Slack thread";
                  } else if (
                    thread.metadata?.external_sinks?.slack?.slackThreadLink
                  ) {
                    if (thread.content?.includes("#")) {
                      channelName = `#${thread.content.split("#")[1].split(" ")[0]
                        }`;
                    } else {
                      channelName = "Slack thread";
                    }
                  } else {
                    channelName = thread.threadName || "Internal thread";
                  }

                  // Truncate if longer than 12 characters
                  const displayName =
                    channelName.length > 12
                      ? `${channelName.substring(0, 12)}...`
                      : channelName;

                  return (
                    <TooltipWrapper
                      tooltipContent={
                        channelName.length > 12 ? channelName : null
                      }
                      side="top"
                      align="center"
                    >
                      <span>{displayName}</span>
                    </TooltipWrapper>
                  );
                })()}
                {(thread.metadata?.external_sinks?.slack?.slackThreadLink ||
                  thread.threadName?.startsWith("Slack:")) && (
                    <a
                      href={
                        thread.metadata?.external_sinks?.slack?.slackThreadLink ||
                        "#"
                      }
                      target="_blank"
                      rel="noreferrer"
                      aria-label={
                        thread.metadata?.external_sinks?.slack?.slackThreadLink
                          ? "View in Slack"
                          : "Slack link not available yet"
                      }
                      className={`ml-1 ${!thread.metadata?.external_sinks?.slack
                        ?.slackThreadLink && "cursor-not-allowed opacity-50"
                        }`}
                      onClick={(e) => {
                        if (
                          !thread.metadata?.external_sinks?.slack?.slackThreadLink
                        ) {
                          e.preventDefault();
                        }
                      }}
                    >
                      <ExternalLink className="h-3.5 w-3.5 text-muted-foreground hover:text-foreground" />
                    </a>
                  )}
              </h3>
            )
          )}
          {activeThreadId !== "new" && (
            <Button
              variant="ghost"
              size="sm"
              className="flex items-center gap-1 px-2 py-1 text-sm text-muted-foreground hover:text-foreground hover:bg-accent rounded-md transition-colors"
              onClick={() => setActiveThreadId(null)}
            >
              <ChevronRight className="h-3.5 w-3.5 rotate-180" />
              Back to threads
            </Button>
          )}
        </div>
        {activeThreadId === "new" && (
          <div ref={contentRef} className="py-4 px-0">
            <div className="flex items-center mb-3">
              <SlackColoredIcon size={18} className="mr-2" />
              <span className="text-sm font-medium">
                Connect a Slack channel
              </span>
              <span className="ml-1.5 text-xs px-1.5 py-0.5 bg-muted/50 text-muted-foreground rounded border border-border/30">
                Optional
              </span>
            </div>
            <Select
              value={slackChannel}
              onValueChange={handleChannelSelect}
              disabled={isPending}
            >
              <SelectTrigger className="w-full rounded-[4px] border border-input">
                <SelectValue
                  placeholder={
                    isPending
                      ? "Loading channels..."
                      : slackChannels.length === 0
                        ? "No Slack channels available"
                        : "Select a channel"
                  }
                />
              </SelectTrigger>
              <SelectContent
                className="max-h-[240px]"
                onScroll={handleScroll}
                position="popper"
                sideOffset={5}
              >
                <div className="sticky top-0 z-10 bg-popover">
                  <div className="flex items-center h-9">
                    <Search className="h-4 w-4 text-muted-foreground ml-2 mr-2" />
                    <input
                      className="flex-1 h-full bg-transparent text-sm outline-none placeholder:text-muted-foreground border-0 py-0 px-0"
                      placeholder="Search"
                      value={searchQuery}
                      onChange={(e) => {
                        setSearchQuery(e.target.value);
                      }}
                    />
                  </div>
                  <div className="border-b w-full mb-[4px]"></div>
                </div>
                {isPending ? (
                  <div className="py-2 px-2 text-sm text-center flex items-center justify-center">
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    Loading channels...
                  </div>
                ) : slackChannels.length === 0 ? (
                  <div className="py-2 px-2 text-sm text-muted-foreground">
                    No channels available. Connect a Slack workspace first.
                  </div>
                ) : (
                  slackChannels
                    ?.filter(
                      (c) =>
                        c.name
                          ?.toLowerCase()
                          ?.includes(searchQuery?.toLowerCase()),
                    )
                    .map((channel) => (
                      <SelectItem key={channel.id} value={channel.name}>
                        {channel.name}{" "}
                        {channel.members ? `(${channel.members} members)` : ""}
                      </SelectItem>
                    ))
                )}
                {isFetchingNextPage && (
                  <div className="py-2 px-2 text-sm text-center flex items-center justify-center">
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    Loading more...
                  </div>
                )}
                {!isFetchingNextPage && hasNextPage && (
                  <div
                    className="py-2 px-2 text-sm text-center text-blue-500 cursor-pointer hover:underline"
                    onClick={() => loadMoreChannels()}
                  >
                    Load more channels
                  </div>
                )}
              </SelectContent>
            </Select>
            {slackChannels.length === 0 && !isPending && (
              <p className="text-xs text-muted-foreground mt-1">
                No Slack channels found. Please make sure your Slack workspace
                is connected.
              </p>
            )}
          </div>
        )}
      </div>

      <div className="flex-1 px-2 pt-4 overflow-auto">
        {activeThreadId === "new" ? (
          <div className="h-full flex flex-col items-center justify-center text-center px-4">
            <div className="bg-muted/30 rounded-full p-4 mb-3">
              <Users size={24} className="text-primary/70" />
            </div>
            <h3 className="text-foreground font-medium text-base">
              New thread
            </h3>
            <p className="text-sm text-muted-foreground mt-1 max-w-xs">
              Chat here or connect with your team on Slack.
            </p>
            <Button
              variant="outline"
              size="sm"
              className="flex items-center gap-1 px-3 py-1.5 text-sm mt-5 rounded-md transition-colors"
              onClick={() => setActiveThreadId(null)}
            >
              <ChevronRight className="h-3.5 w-3.5 rotate-180" />
              Back to threads
            </Button>
          </div>
        ) : (
          <div className="space-y-[6px] pb-2">
            {thread && (
              <div className="flex gap-4 items-start group hover:bg-accent/50 rounded py-1 px-2">
                <div className="flex-shrink-0 w-10 flex flex-col items-center mt-1">
                  <Avatar
                    src={avatarUrl}
                    fallbackImage={avatarUrl}
                    fallbackText={userName}
                    fallbackTextClassnames="h-9 min-w-9"
                    imgClassnames="h-9 min-w-9"
                  />
                </div>
                <div className="w-full">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <span className="text-[14px] font-medium">
                        {userName || ""}
                      </span>

                      <TooltipWrapper
                        tooltipContent={format(
                          new Date(thread.createdAt),
                          "d MMM, h:mm a",
                        )}
                      >
                        {" "}
                        <span className="text-xs text-muted-foreground">
                          {getTimeAgo(thread.createdAt)}
                        </span>
                      </TooltipWrapper>
                      {thread.isEdited && (
                        <span className="text-xs text-muted-foreground relative top-[1.5px]">
                          (edited)
                        </span>
                      )}
                    </div>
                    <div className="flex items-center gap-1">
                      <div className="opacity-0 group-hover:opacity-100 transition-opacity">
                        <EmojiPicker
                          onClickOutside={() => { }}
                          onEmojiSelect={(emoji) => {
                            setEmojiMessage(thread.id);
                            addEmoji({ name: emoji.id });
                          }}
                          open={thread.id === emojiPickerOpenId}
                          setOpen={(open) => {
                            if (open) {
                              setEmojiMessage(thread.id);
                            }
                            setEmojiPickerOpenId(open ? thread.id : null);
                          }}
                          pickerClassname="border-none shadow-none"
                          pickerEmojiSize={14}
                        />
                      </div>
                      {isAllowedToEdit && (
                        <MessageActions
                          message={thread}
                          onEdit={handleEditMessage}
                          onDelete={handleDeleteMessage}
                          setDeleteMessageId={setDeleteMessageId}
                          setEditOpen={setEditOpen}
                        />
                      )}
                    </div>
                  </div>

                  {editOpen.state && editOpen?.id === thread.id ? (
                    <Tiptap
                      content={thread.contentHtml}
                      setContent={() => { }}
                      onSend={(editor) => saveEditedMessage(editor, thread.id)}
                      localFiles={editLocalFiles}
                      setLocalFiles={setEditLocalFiles}
                      isFileUploadEnabled={false}
                      setEditOpen={setEditOpen}
                      threadType={ThreadType.INTERNAL}
                    />
                  ) : (
                    <ConversationWrapper
                      className="text-sm"
                      style={{ marginTop: "2px" }}
                      // biome-ignore lint/security/noDangerouslySetInnerHtml: <explanation>
                      dangerouslySetInnerHTML={{
                        __html: DOMPurify.sanitize(highlightMentions(thread.contentHtml)),
                      }}
                    />
                  )}

                  {data.emojis && (
                    <Reactions conversation={thread} emojis={data.emojis} />
                  )}
                  {thread.attachments?.length > 0 && (
                    <div className="mt-2">
                      <AttachmentPreview
                        urls={thread.attachments.map((item) => ({
                          url: item.url,
                          type: item.contentType,
                        }))}
                      />
                    </div>
                  )}
                </div>
              </div>
            )}
            {(replyData || []).length > 0 &&
              replyData.map((message, id) => {
                const userName = message.impersonatedUserName ?? message.author;
                const avatarUrl =
                  message.impersonatedUserAvatar ?? message.authorAvatarUrl;

                const isAllowedToEdit = message.authorId === currentUserId;

                return (
                  <Fragment key={message.id + id}>
                    <div className="flex gap-4 items-start group hover:bg-accent/50 rounded py-1 px-2">
                      <div className="flex-shrink-0 w-10 flex flex-col items-center mt-1">
                        <Avatar
                          src={avatarUrl}
                          fallbackImage={avatarUrl}
                          fallbackText={userName}
                          fallbackTextClassnames="h-9 min-w-9"
                          imgClassnames="h-9 min-w-9"
                        />
                      </div>
                      <div className="w-full">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <span className="text-[14px] font-medium">
                              {userName || ""}
                            </span>
                            <TooltipWrapper
                              tooltipContent={format(
                                new Date(message.createdAt),
                                "d MMM, h:mm a",
                              )}
                            >
                              {" "}
                              <span className="text-xs text-muted-foreground">
                                {getTimeAgo(message.createdAt)}
                              </span>
                            </TooltipWrapper>
                            {message.isEdited && (
                              <span className="text-xs text-muted-foreground relative top-[1.5px]">
                                (edited)
                              </span>
                            )}
                          </div>
                          <div className="flex items-center gap-1">
                            <div className="opacity-0 group-hover:opacity-100 transition-opacity">
                              <EmojiPicker
                                onClickOutside={() => { }}
                                onEmojiSelect={(emoji) => {
                                  setEmojiMessage(message.id);
                                  addEmoji({ name: emoji.id });
                                }}
                                open={message.id === emojiPickerOpenId}
                                setOpen={(open) => {
                                  if (open) {
                                    setEmojiMessage(message.id);
                                  }
                                  setEmojiPickerOpenId(
                                    open ? message.id : null,
                                  );
                                }}
                                pickerClassname="border-none shadow-none"
                                pickerEmojiSize={14}
                              />
                            </div>
                            {isAllowedToEdit && (
                              <MessageActions
                                message={message}
                                onEdit={handleEditMessage}
                                onDelete={handleDeleteMessage}
                                setDeleteMessageId={setDeleteMessageId}
                                setEditOpen={setEditOpen}
                              />
                            )}
                          </div>
                        </div>

                        {editOpen.state && editOpen?.id === message.id ? (
                          <Tiptap
                            content={message.contentHtml}
                            setContent={() => { }}
                            onSend={(editor) =>
                              saveEditedMessage(editor, message.id)
                            }
                            localFiles={editLocalFiles}
                            setLocalFiles={setEditLocalFiles}
                            isFileUploadEnabled={false}
                            setEditOpen={setEditOpen}
                            threadType={ThreadType.INTERNAL}
                          />
                        ) : (
                          <ConversationWrapper
                            className="text-sm"
                            style={{ marginTop: "2px" }}
                            // biome-ignore lint/security/noDangerouslySetInnerHtml: <explanation>
                            dangerouslySetInnerHTML={{
                              __html: DOMPurify.sanitize(message.contentHtml),
                            }}
                          />
                        )}

                        {data.emojis && (
                          <Reactions
                            conversation={message}
                            emojis={data.emojis}
                          />
                        )}
                        {message.attachments?.length > 0 && (
                          <div className="mt-2">
                            <AttachmentPreview
                              urls={message.attachments.map((item) => ({
                                url: item.url,
                                type: item.contentType,
                              }))}
                            />
                          </div>
                        )}
                      </div>
                    </div>
                    {/* {id === replyData?.length - 1 && (

                    )} */}
                  </Fragment>
                );
              })}

            <div ref={scrollRef} className="h-2" />
          </div>
        )}
      </div>

      <div className="px-2 py-2 pb-4 bg-background w-full">
        <Tiptap
          content={content}
          setContent={setContent}
          onSend={(editor) => addReplyToComment(editor)}
          localFiles={localFiles}
          setLocalFiles={setLocalFiles}
          className="max-h-96 w-full"
          threadType={ThreadType.INTERNAL}
        />
      </div>
    </div>
  );
};

export default ActiveThread;
